; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[platformio]
default_envs = Protoboard

[env:Protoboard]
platform = teensy@4.18
board = teensy36
framework = arduino
lib_extra_dirs = ~/Documents/Arduino/libraries
platform_packages = platformio/framework-arduinoteensy@1.158
build_flags =
	-D USB_SERIAL_HID
lib_deps =
	SD
